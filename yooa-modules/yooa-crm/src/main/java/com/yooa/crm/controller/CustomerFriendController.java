package com.yooa.crm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.crm.api.domain.query.CustomerFriendQuery;
import com.yooa.crm.service.CustomerFriendService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/customer/friend")
@RequiredArgsConstructor
public class CustomerFriendController extends BaseController {

    private final CustomerFriendService customerFriendService;

    /**
     * 投放 - 注册列表
     */
    @GetMapping("/pitcher/list")
    public AjaxResult list(Page page, CustomerFriendQuery query) {
        return AjaxResult.success();
    }


}
