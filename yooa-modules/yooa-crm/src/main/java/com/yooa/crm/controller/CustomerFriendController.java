package com.yooa.crm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.crm.api.domain.query.CustomerFriendQuery;
import com.yooa.crm.api.domain.vo.RegisterVo;
import com.yooa.crm.service.CustomerFriendService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;


@RestController
@RequestMapping("/customer/friend")
@RequiredArgsConstructor
@Validated
public class CustomerFriendController extends BaseController {

    private final CustomerFriendService customerFriendService;

    /**
     * 投放 - 注册列表
     */
    @GetMapping("/pitcher/list")
    public AjaxResult list(Page<RegisterVo> page, @Valid CustomerFriendQuery query) {
        return AjaxResult.success(page.setRecords(customerFriendService.pitcherRegisterList(page, query, true)));
    }

    /**
     * 投放 - 导出注册列表
     */
    @PostMapping("/pitcher/list/export")
    public void export(HttpServletResponse response, @Valid CustomerFriendQuery query) {
        List<RegisterVo> list = customerFriendService.pitcherRegisterList(new Page<RegisterVo>().setSize(-1), query, false);
        ExcelUtil<RegisterVo> util = new ExcelUtil<>(RegisterVo.class);
        util.exportExcel(response, list, "投放注册列表");
    }

}
