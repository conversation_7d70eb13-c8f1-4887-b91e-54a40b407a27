package com.yooa.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.crm.api.domain.CrmCustomerOrder;
import com.yooa.crm.api.domain.CrmFriend;
import com.yooa.crm.api.domain.dto.CustomerStatisticsDto;
import com.yooa.crm.api.domain.dto.FriendEmployeeDto;
import com.yooa.crm.api.domain.dto.FriendRegisterChargeDto;
import com.yooa.crm.api.domain.query.*;
import com.yooa.crm.api.domain.vo.*;
import com.yooa.extend.api.domain.ExtendVermicelli;
import com.yooa.extend.api.domain.dto.CommonalityDto;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 好友 - 数据层
 */
public interface CrmFriendMapper extends BaseMapper<CrmFriend> {

    /**
     * 查询好友列表
     */
    List<CrmCommonFriendVo> selectListCustom(Page<CrmCommonFriendVo> page, @Param("query") FriendQuery query);

    /**
     * 查询好友客户列表
     */
    List<FriendCustomerVo> selectFriendCustomerList(Page<FriendCustomerVo> page,
                                                    @Param("query") CustomerFriendQuery query,
                                                    @Param("userIds") List<Long> userIds);

    /**
     * 新查询好友客户列表
     */
    List<FriendCustomerVo> selectFriendCustomerNewList(Page<FriendCustomerVo> page,
                                                       @Param("query") CustomerFriendQuery query,
                                                       @Param("userIds") List<Long> userIds);

    /**
     * 查询好友客户列表
     */
    List<FriendCustomerVo> exportFriendCustomerList(@Param("query") CustomerFriendQuery query,
                                                    @Param("userIds") List<Long> userIds);

    /**
     * 查询好友客户列表
     */
    List<FriendCustomerVo> exportFriendCustomerNewList(@Param("query") CustomerFriendQuery query,
                                                       @Param("userIds") List<Long> userIds);

    List<FreeExportVo> listFreeExport(@Param("query") CustomerFriendQuery query,
                                      @Param("userIds") List<Long> userIds);

    /**
     * 推广维护信息集
     *
     * @param friendId
     * @return
     */
    List<ExtendVo> extendJoinList(@Param("friendId") Long friendId);

    /**
     * VIP维护信息集
     *
     * @param friendId
     * @return
     */
    List<ServeVo> vipJoinList(@Param("friendId") Long friendId);

    /**
     * 运营维护信息集
     *
     * @param friendId
     * @return
     */
    List<OperatorVo> operateJoinList(@Param("friendId") Long friendId);

    /**
     * 查询运营好友客户列表
     */
    List<OperateFriendCustomerVo> selectOperateFriendCustomerList(Page<OperateFriendCustomerVo> page,
                                                                  @Param("query") OperateCustomerFriendQuery query,
                                                                  @Param("userIds") List<Long> userIds);

    /**
     * 新查询运营好友客户列表
     */
    List<OperateFriendCustomerVo> selectOperateFriendCustomerNewList(Page<OperateFriendCustomerVo> page,
                                                                     @Param("query") OperateCustomerFriendQuery query,
                                                                     @Param("userIds") List<Long> userIds);

    /**
     * 导出运营好友客户列表
     */
    List<OperateFriendCustomerVo> exportOperateFriendCustomerList(@Param("query") OperateCustomerFriendQuery query,
                                                                  @Param("userIds") List<Long> userIds);

    /**
     * 新导出运营好友客户列表
     */
    List<OperateFriendCustomerVo> exportOperateFriendCustomerNewList(@Param("query") OperateCustomerFriendQuery query,
                                                                     @Param("userIds") List<Long> userIds);

    /**
     * 查询注册列表
     */
    List<RegisterVo> selectRegisterAllList(@Param("page") Page<RegisterVo> page,
                                           @Param("query") CustomerFriendQuery query,
                                           @Param("userIds") List<Long> userIds);

    /**
     * 导出注册列表
     */
    List<RegisterVo> exportRegisterAllList(@Param("query") CustomerFriendQuery query,
                                           @Param("userIds") List<Long> userIds);

    /**
     * 查询注册列表
     */
    List<RegisterVo> selectRegisterExtendList(@Param("page") Page<RegisterVo> page,
                                              @Param("query") CustomerFriendQuery query,
                                              @Param("userIds") List<Long> userIds);

    /**
     * 查询投手注册列表
     */
    List<RegisterVo> selectPitcherRegisterAllList(@Param("page") Page<RegisterVo> page,
                                                  @Param("query") CustomerFriendQuery query,
                                                  @Param("userIds") List<Long> userIds);

    /**
     * 导出投手注册列表
     */
    List<RegisterVo> exportPitcherRegisterAllList(@Param("query") CustomerFriendQuery query,
                                                  @Param("userIds") List<Long> userIds);

    /**
     * 查询投手注册列表
     */
    List<RegisterVo> selectPitcherRegisterExtendList(@Param("page") Page<RegisterVo> page,
                                                     @Param("query") CustomerFriendQuery query,
                                                     @Param("userIds") List<Long> userIds);

    /**
     * 查询好友列表
     */
    List<FriendDetailsVo> selectFriendList(@Param("page") Page<FriendDetailsVo> page,
                                           @Param("query") CustomerFriendQuery query,
                                           @Param("userIds") List<Long> userIds);

    /**
     * 投放 - 查询好友列表
     */
    List<FriendDetailsVo> pitcherSelectFriendList(@Param("page") Page<FriendDetailsVo> page,
                                           @Param("query") CustomerFriendQuery query);

    /**
     * 查询充值金额
     *
     * @param query
     * @return
     */
    List<FriendCustomerVo> selectRecharge(@Param("query") CustomerFriendQuery query,
                                          @Param("list") List<FriendCustomerVo> list);

    /**
     * 查询运营打赏金额
     *
     * @return
     */
    List<OperateFriendCustomerVo> selectOperateRecharge(@Param("customerIds") List<Long> customerIds,
                                                        @Param("pyOperateIds") List<Long> pyOperateIds,
                                                        @Param("anchorIds") List<Long> anchorIds,
                                                        @Param("query") OperateCustomerFriendQuery query);

    /**
     * 查询粉丝登记
     *
     * @param query
     * @return
     */
    List<FriendFansVo> selectFansList(@Param("query") CustomerFriendQuery query);

    /**
     * 查询运营粉丝登记
     *
     * @param query
     * @return
     */
    List<OperateFriendCustomerVo> selectOperateFansList(@Param("query") OperateCustomerFriendQuery query);

    /**
     * 查询自己领取的所有好友下所有客户ID集
     */
    List<Long> selMycustomerIds(@Param("userid") Long userId, @Param("tfType") Integer tfType);

    /**
     * 各字段分组统计
     *
     * @param dto       查询条件
     * @param groupStr  分组字段(字典类型)
     * @param groupStr1 关联字段
     */
    List<GroupPeopleNumberVo> GroupPeopleNumber(@Param("dto") CustomerStatisticsDto dto,
                                                @Param("groupStr") String groupStr,
                                                @Param("groupStr1") String groupStr1);

    /**
     * 各字段分组统计
     *
     * @param dto 查询条件
     */
    List<GroupPeopleNumberVo> districtGroupPeople(@Param("dto") CustomerStatisticsDto dto);

    /**
     * 性别年龄分组字段人数统计
     *
     * @param dto 查询条件
     */
    List<SexAgePeopleNumberVo> SexAgePeopleNumber(@Param("dto") CustomerStatisticsDto dto);

    /**
     * 查询好友业绩的组成
     */
    IPage<FriendCustomerVo> customerFriend(Page<?> page, @Param("dto") CommonalityDto dto);

    /**
     * 查询注册业绩的组成
     */
    IPage<FriendCustomerVo> customerRegister(Page<?> page, @Param("dto") CommonalityDto dto);

    /*---------------------------------------------内部使用--------------------------------------------------------------------*/

    /**
     * 查询创建者不为自己的记录是否大于15天没修改
     *
     * @param d1 15天前的时间戳(十位)
     * @param d2 30天前的时间戳(十位)
     */
    List<Long> friendSchedulesSelect(@Param("d1") Date d1, @Param("d2") Date d2);

    /**
     * 修改创建者不为自己的记录是否大于15天没修改
     *
     * @param ids 要修改的好友ID集
     */
    int friendSchedulesUpdate(@Param("ids") List<Long> ids);

    /**
     * 查询用户的好友
     *
     * @param dto 统一入参条件
     */
    List<FriendRegisterChargeVo> getFriend(@Param("dto") FriendRegisterChargeDto dto);

    /**
     * 查询用户的注册数
     *
     * @param dto 统一入参条件
     */
    List<FriendRegisterChargeVo> getRegister(@Param("dto") FriendRegisterChargeDto dto);

    /**
     * 传deptId就只返回一条,只传hierarchy就返回多条
     */

    /**
     * 查询部门的好友
     */
    List<FriendRegisterChargeVo> getFriendDept(@Param("dto") FriendRegisterChargeDto dto);

    /**
     * 查询部门的注册数
     */
    List<FriendRegisterChargeVo> getRegisterDept(@Param("dto") FriendRegisterChargeDto dto);

    /**
     * 查询部门的好友根据时间分组
     */
    List<FriendRegisterChargeVo> getFriendDeptTimeGroup(@Param("dto") FriendRegisterChargeDto dto);

    /**
     * 查询部门的注册数根据时间分组
     */
    List<FriendRegisterChargeVo> getRegisterDeptTimeGroup(@Param("dto") FriendRegisterChargeDto dto);

    /**
     * 查询用户的好友根据时间分组
     */
    List<FriendRegisterChargeVo> getFriendUserTimeGroup(@Param("dto") FriendRegisterChargeDto dto);

    /**
     * 查询用户的注册数根据时间分组
     */
    List<FriendRegisterChargeVo> getRegisterUserTimeGroup(@Param("dto") FriendRegisterChargeDto dto);

    /**
     * 查询用户的领取数和客户数
     *
     * @param ids 用户id集
     */
    List<ReceiveSettingVo> selUserReceiveNumber(@Param("ids") List<Long> ids);

    /**
     * 查询部门的领取数和客户数
     *
     * @param ids 用户id集
     */
    List<ReceiveSettingVo> selDeptReceiveNumber(@Param("ids") List<Long> ids);

    /**
     * 查询时间段内领取的好友下的所有客户ID集
     */
    List<Long> selFriendEmployeeCustomerIds(@Param("dto") FriendEmployeeDto dto);

    /**
     * 查询时间段内领取的好友下的所有好友ID集
     */
    List<Long> selFriendEmployeeFriendIds(@Param("dto") FriendEmployeeDto dto);

    /**
     * 查询时间段内领取的好友下的所有客户的充值金额
     */
    List<CrmCustomerOrder> selFriendEmployeeMoney(@Param("dto") FriendEmployeeDto dto);

    /**
     * 查询时间段内领取的好友下的所有客户的好友数
     */
    List<FriendRegisterChargeVo> selFriendEmployeeFriend(@Param("dto") FriendEmployeeDto dto);

    /**
     * 查询时间段内领取的好友下的所有客户的注册数
     */
    List<FriendRegisterChargeVo> selFriendEmployeeRegister(@Param("dto") FriendEmployeeDto dto);

    /**
     * 临时使用
     **/

    List<ExtendVermicelli> selFriend(@Param("friendId") Long friendId);

    List<Long> selFriendIds();

    int delFriend(@Param("friendId") Long friendId);

    List<Long> getCustomerIdList(@Param("friendId") Long friendId);

    void updateStatusByCustomerId(@Param("customerId") String customerId, @Param("extendId") String afterExtendId);

    List<CrmCommonFriendVo> selReceiveCommonFriend(Page<CrmCommonFriendVo> page, @Param("query") FriendQuery query, @Param("userId") Long userId);

    List<PollingFriendVo> pollingFriendList(Page<PollingFriendVo> page, @Param("query") PollingFriendQuery query);

    List<PollingFriendInvalidVo> pollingInvalidList(Page<PollingFriendInvalidVo> page, @Param("query") PollingFriendQuery query);

    List<PollingStatisticsVo> statisticsList(Page<PollingStatisticsVo> page,  @Param("query") PollingFriendQuery query);

    List<FriendSequenceIdVo> getChatSequenceList();

    /**
     * 获取VIP充值订单
     */
    List<VipRechargeOrderVo> getVipRechargeOrder(Page page,@Param("query") VipRechargeOrderQuery query);

    /**
     * 通过客户id找好友数据
     * @param customerId
     */
    FriendVo getCustomerFriend(String customerId);

}




