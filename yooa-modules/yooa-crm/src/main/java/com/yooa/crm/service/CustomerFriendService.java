package com.yooa.crm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.crm.api.domain.CrmCustomerFriend;
import com.yooa.crm.api.domain.query.CustomerFriendQuery;
import com.yooa.crm.api.domain.vo.RegisterVo;

import java.util.List;

/**
 * 客户好友关联 - 服务层
 */
public interface CustomerFriendService extends IService<CrmCustomerFriend> {

    /**
     * 根据客户id获取其在绑的记录
     */
    CrmCustomerFriend getBindByCustomerId(Long customerId);

    /**
     * 根据好友id获取其下在绑记录
     */
    List<CrmCustomerFriend> getBindByFriendId(Long friendId);

    /**
     * 投手注册列表
     */
    List<RegisterVo> pitcherRegisterList(Page<RegisterVo> page, CustomerFriendQuery query, boolean bl);

}
