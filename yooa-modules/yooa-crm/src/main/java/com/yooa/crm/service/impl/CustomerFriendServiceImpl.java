package com.yooa.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.crm.api.domain.CrmCustomerFriend;
import com.yooa.crm.mapper.CrmCustomerFriendMapper;
import com.yooa.crm.service.CustomerFriendService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户好友关联 - 服务实现层
 */
@Service
public class CustomerFriendServiceImpl extends ServiceImpl<CrmCustomerFriendMapper, CrmCustomerFriend> implements CustomerFriendService {
    @Override
    public CrmCustomerFriend getBindByCustomerId(Long customerId) {
        return getOne(new LambdaQueryWrapper<CrmCustomerFriend>()
                .eq(CrmCustomerFriend::getCustomerId, customerId)
                .eq(CrmCustomerFriend::getStatus, 0)
                .last("limit 1")
        );
    }
    
    @Override
    public List<CrmCustomerFriend> getBindByFriendId(Long friendId) {
        return list(new LambdaQueryWrapper<CrmCustomerFriend>()
                .eq(CrmCustomerFriend::getFriendId, friendId)
                .eq(CrmCustomerFriend::getStatus, 0)
        );
    }
}
