package com.yooa.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.common.datascope.annotation.DataScope;
import com.yooa.crm.api.domain.CrmCustomerFriend;
import com.yooa.crm.api.domain.query.CustomerFriendQuery;
import com.yooa.crm.api.domain.query.CustomerJoinAnchorQuery;
import com.yooa.crm.api.domain.vo.CustomerJoinAnchorVo;
import com.yooa.crm.api.domain.vo.RegisterVo;
import com.yooa.crm.mapper.CrmCustomerFriendMapper;
import com.yooa.crm.mapper.CrmFriendMapper;
import com.yooa.crm.service.CustomerFriendService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户好友关联 - 服务实现层
 */
@Service
@AllArgsConstructor
public class CustomerFriendServiceImpl extends ServiceImpl<CrmCustomerFriendMapper, CrmCustomerFriend> implements CustomerFriendService {

    private final CrmFriendMapper friendMapper;
    @Override
    public CrmCustomerFriend getBindByCustomerId(Long customerId) {
        return getOne(new LambdaQueryWrapper<CrmCustomerFriend>()
                .eq(CrmCustomerFriend::getCustomerId, customerId)
                .eq(CrmCustomerFriend::getStatus, 0)
                .last("limit 1")
        );
    }
    
    @Override
    public List<CrmCustomerFriend> getBindByFriendId(Long friendId) {
        return list(new LambdaQueryWrapper<CrmCustomerFriend>()
                .eq(CrmCustomerFriend::getFriendId, friendId)
                .eq(CrmCustomerFriend::getStatus, 0)
        );
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<RegisterVo> pitcherRegisterList(Page<RegisterVo> page, CustomerFriendQuery query, boolean bl) {
        if (bl) {
            // 分页查询
            return friendMapper.selectPitcherRegisterList(page, query);
        } else {
            // 不分页导出
            return friendMapper.selectPitcherRegisterList(null, query);
        }
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<CustomerJoinAnchorVo> pitcherJoinAnchorList(Page<CustomerJoinAnchorVo> page, CustomerJoinAnchorQuery query, boolean bl) {
        // 设置模块类型为投手
        query.setModuleType("pitcher");

        if (bl) {
            // 分页查询
            return friendMapper.selectPitcherJoinAnchorList(page, query);
        } else {
            // 不分页导出
            return friendMapper.exportPitcherJoinAnchorList(query);
        }
    }
}
