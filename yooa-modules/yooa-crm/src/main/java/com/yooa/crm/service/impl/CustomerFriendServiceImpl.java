package com.yooa.crm.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.common.core.constant.DictConstants;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.crm.api.domain.CrmCustomerFriend;
import com.yooa.crm.api.domain.query.CustomerFriendQuery;
import com.yooa.crm.api.domain.vo.RegisterVo;
import com.yooa.crm.mapper.CrmCustomerFriendMapper;
import com.yooa.crm.mapper.CrmFriendMapper;
import com.yooa.crm.service.CustomerFriendService;
import com.yooa.system.api.RemoteUserService;
import com.yooa.system.api.domain.SysUser;
import com.yooa.system.api.domain.query.UserQuery;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户好友关联 - 服务实现层
 */
@Service
@AllArgsConstructor
public class CustomerFriendServiceImpl extends ServiceImpl<CrmCustomerFriendMapper, CrmCustomerFriend> implements CustomerFriendService {

    private final CrmFriendMapper friendMapper;
    private final RemoteUserService remoteUserService;
    @Override
    public CrmCustomerFriend getBindByCustomerId(Long customerId) {
        return getOne(new LambdaQueryWrapper<CrmCustomerFriend>()
                .eq(CrmCustomerFriend::getCustomerId, customerId)
                .eq(CrmCustomerFriend::getStatus, 0)
                .last("limit 1")
        );
    }
    
    @Override
    public List<CrmCustomerFriend> getBindByFriendId(Long friendId) {
        return list(new LambdaQueryWrapper<CrmCustomerFriend>()
                .eq(CrmCustomerFriend::getFriendId, friendId)
                .eq(CrmCustomerFriend::getStatus, 0)
        );
    }

    @Override
    public List<RegisterVo> pitcherRegisterList(Page<RegisterVo> page, CustomerFriendQuery query, boolean bl) {
        List<Long> userIds = CollUtil.newArrayList();
        if (query.getDataScope() == 1 && SecurityUtils.getLoginUser().getSysUser().getUserRole().equals(DictConstants.SYS_USER_ROLE_ADMIN)) {
            // 管理员看下级的查所有
        } else {
            if (query.getDataScope() == 1) {
                userIds.addAll(remoteUserService.getList(UserQuery.builder().build(), SecurityConstants.INNER)
                        .getData().stream().map(SysUser::getUserId).collect(Collectors.toList()));
            }
        }

        List<RegisterVo> list = new ArrayList<>();

        if (bl) {
            // 分页查询
            if (CollUtil.isNotEmpty(query.getDataScopeType()) &&
                    (query.getDataScopeType().contains(2) || query.getDataScopeType().contains(3) || query.getDataScopeType().contains(0))) {
                list = friendMapper.selectPitcherRegisterAllList(page, query, userIds);       // 查询全部
            } else {
                list = friendMapper.selectPitcherRegisterExtendList(page, query, userIds);    // 查询推广或者渠道(默认为查询推广)
            }
        } else {
            // 不分页导出
            list = friendMapper.exportPitcherRegisterAllList(query, userIds);
        }

        return list;
    }
}
