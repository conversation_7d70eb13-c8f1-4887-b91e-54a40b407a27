package com.yooa.crm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.common.datascope.annotation.DataScope;
import com.yooa.crm.api.domain.CrmCustomerJoinAnchor;
import com.yooa.crm.api.domain.query.CustomerJoinAnchorQuery;
import com.yooa.crm.api.domain.query.CustomerOperateJoinAnchorQuery;
import com.yooa.crm.api.domain.vo.CustomerJoinAnchorVo;
import com.yooa.crm.api.domain.vo.CustomerOperateJoinAnchorVo;
import com.yooa.crm.mapper.CrmCustomerJoinAnchorMapper;
import com.yooa.crm.service.CustomerJoinAnchorService;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 客户主播交接 - 服务实现层
 */
@Service
@RequiredArgsConstructor
public class CustomerJoinAnchorServiceImpl extends ServiceImpl<CrmCustomerJoinAnchorMapper, CrmCustomerJoinAnchor>
        implements CustomerJoinAnchorService {

    @Resource(name = "customThreadPoolTaskExecutor")
    private ThreadPoolTaskExecutor executor;

    /**
     * 查询条件 交接状态-已交接 交接类型-首次交接 客户id
     */
    @Override
    public List<CrmCustomerJoinAnchor> getCustomerJoinAnchorByCustomerId(Long customerId, LocalDateTime addTime) {
        return baseMapper.getCustomerJoinAnchorByCustomerId(customerId, addTime);

    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<CustomerJoinAnchorVo> getList(Page page, CustomerJoinAnchorQuery customerJoinAnchorQuery) {
        // 异步获取ID列表
        CompletableFuture<List<Long>> idsFuture = CompletableFuture.supplyAsync(() ->
                baseMapper.getIds(page, customerJoinAnchorQuery), executor
        );

        // 当ID列表获取完成后，异步查询详细信息
        CompletableFuture<List<CustomerJoinAnchorVo>> detailFuture = idsFuture.thenCompose(ids -> {
            if (CollectionUtils.isEmpty(ids)) {
                return CompletableFuture.completedFuture(Collections.emptyList());
            }
            return CompletableFuture.supplyAsync(() -> baseMapper.queryDetailById(ids), executor);
        });

        // 处理结果并设置无效类型
        return detailFuture.thenApply(detailList ->
                detailList.stream()
                        .peek(vo -> {
                            if (vo != null && vo.getIsItValid() != null && vo.getIsItValid() == 1) {
                                LocalDateTime beginTime = vo.getBeginTime();
                                LocalDateTime endTime = vo.getEndTime();

                                // 无效类型区分
                                // 未绑定的 无效类型为未绑定
                                if (StrUtil.isNotBlank(vo.getBindType()) && StrUtil.equals(vo.getBindType(), "0")) {
                                    vo.setInvalidType(3);
                                } else if (beginTime != null && endTime != null) {
                                    // 好友绑定表中的结束时间大于开始时间 属于非最早交接的无效数据
                                    vo.setInvalidType(endTime.isAfter(beginTime) ? 1 : 2);
                                }

                            }
                        })
                        .toList()
        ).join();
    }


    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<CustomerOperateJoinAnchorVo> getOperatorJoinAnchorList(Page page, CustomerOperateJoinAnchorQuery query) {
        List<Long> ids = baseMapper.getOperatorJoinIds(page, query);
        if (CollectionUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return queryOperteJoinAnchorDetail(ids);
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<CustomerJoinAnchorVo> getListExport(Page page, CustomerJoinAnchorQuery customerJoinAnchorQuery) {
        return baseMapper.queryDetail(customerJoinAnchorQuery);
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<CustomerOperateJoinAnchorVo> getOperatorJoinAnchorListExport(CustomerOperateJoinAnchorQuery query) {
        return baseMapper.getOperatorJoinAnchorListExport(query);
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<CustomerJoinAnchorVo> getPitcherList(Page<CustomerJoinAnchorVo> page, CustomerJoinAnchorQuery query) {
        // 设置模块类型为投手
        query.setModuleType("pitcher");
        return baseMapper.selectPitcherJoinAnchorList(page, query);
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<CustomerJoinAnchorVo> getPitcherListExport(CustomerJoinAnchorQuery query) {
        // 设置模块类型为投手
        query.setModuleType("pitcher");
        return baseMapper.exportPitcherJoinAnchorList(query);
    }

    @Override
    public Long existsDuplicateBinding(Long customerId, Long friendId, Long extendId) {
        return baseMapper.selectCount(new LambdaQueryWrapper<CrmCustomerJoinAnchor>()
                .eq(CrmCustomerJoinAnchor::getCustomerId, customerId)
                .eq(CrmCustomerJoinAnchor::getStatus, "1")
                .eq(CrmCustomerJoinAnchor::getFriendId, friendId)
                .eq(CrmCustomerJoinAnchor::getExtendId, extendId));
    }

    private List<CustomerOperateJoinAnchorVo> queryOperteJoinAnchorDetail(List<Long> ids) {
        return baseMapper.queryOperateJoinAnchorById(ids);
    }

}




